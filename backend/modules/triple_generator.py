import re

from yfflow import <PERSON>f<PERSON>ogger

from backend.models import TripleData
from backend.modules.graph import BaseGraphMapper

logger = YfLogger(__name__)


class TripleGenerator:
    def __init__(self, graph_mapper: BaseGraphMapper) -> None:
        self.graph_mapper = graph_mapper

    def generate(
        self, space: str, version: str, export_type: str, with_media: bool = False
    ) -> str:
        """生成三元组数据"""
        triples = ""
        entity_triples = self.export_entity_triples(
            space, version, export_type, with_media
        )
        triples += entity_triples
        triples += self.export_relation_triples(space, version, export_type)
        return triples

    def export_entity_triples(
        self, space: str, version: str, export_type: str, with_media: bool = False
    ) -> str:
        """生成实体三元组数据"""
        triples = ""
        skip = 0
        limit = 1000
        while True:
            data = self.graph_mapper.export_entity_triples(
                space, skip, limit, version, export_type
            )
            skip += limit
            lines = ""
            for item in data["data"]:
                line = ""
                if re.search(r"^-?\d+.?\d+$", str(item[2])):
                    line = "<{0}>\t<{1}>\t{2}\t.\n".format(item[0], item[1], item[2])
                else:
                    line = '<{0}>\t<{1}>\t"{2}"\t.\n'.format(item[0], item[1], item[2])
                lines += line
            triples += lines
            if data["total"] < limit:
                break
        return triples

    def export_relation_triples(
        self, space: str, version: str, export_type: str
    ) -> str:
        """生成关系三元组数据"""
        skip = 0
        limit = 1000
        triples = ""
        while True:
            data = self.graph_mapper.export_relation_triples(
                space, skip, limit, version, export_type
            )
            skip += limit
            lines = ""
            for item in data["data"]:
                line = "<{0}>\t<{1}>\t<{2}>\t.\n".format(item[0], item[1], item[2])
                lines += line
            triples += lines
            if data["total"] < limit:
                break
        return triples

    def generate_by_extract(self, task_id: int) -> str:
        """生成三元组数据"""
        triples = ""
        extract_triples = TripleData.objects.filter(
            task__id=task_id,
            second_decimation=False,
            valid_status=TripleData.ValidStatusType.VALID,
        ).all()
        for triple in extract_triples:
            if triple.type == TripleData.TypeChoices.PROPERTY:
                triples += (
                    f'<{triple.subject}>\t<{triple.predicate}>\t"{triple.object}"\t.\n'
                )
            else:
                triples += (
                    f"<{triple.subject}>\t<{triple.predicate}>\t<{triple.object}>\t.\n"
                )
        return triples
