from typing import List

from pydantic import BaseModel


class Triples(BaseModel):
    ontology: List[str] = []
    property: List[str] = []
    relation: List[str] = []

    def __add__(self, other: 'Triples'):
        self.ontology += other.ontology
        self.property += other.property
        self.relation += other.relation
        return self


class TripleClassifier:

    @classmethod
    def classify(cls, content: str, type_: str = '') -> Triples:
        """三元组拆分，分为本体三元组、属性三元组、关系三元组"""
        triples = Triples()
        for triple in content.split('\n'):
            # 去除空格
            try:
                subject, predicate, object_, _ = triple.strip().split('\t')
            except Exception:
                continue
            if object_.startswith('<'):
                if cls._get_out_of_arrow(predicate) == '属于':
                    triples.property.append(f'{subject}\t<_type>\t"{cls._get_out_of_arrow(object_)}"\t.\n')
                triples.relation.append(triple)
            else:
                if cls._get_out_of_arrow(predicate) == '_type' and type_ == 'ontology':
                    triples.ontology.append(triple)
                    continue
                triples.property.append(triple)
        return triples

    @staticmethod
    def _get_out_of_arrow(string: str) -> str:
        return string[1:-1]

    @staticmethod
    def _get_out_of_quotation(string: str) -> str:
        return string.replace('"', '').replace("'", '')
