from typing import List, Union, overload

from yfflow import Yf<PERSON>ogger
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb
from yunfu.db.graph.models import Edge, EdgeType, Node, NodeProp, NodeType

from backend.utils import TimeUtils

from .base_graph_mapper import BaseGraphMapper

logger = YfLogger(__name__)


class NebulaGraphMapper(BaseGraphMapper):
    graph_db: NebulaGraphDb
    node_private_props = ["show_name", "type", "eid", "create_time", "update_time"]
    edge_private_props = ["rid", "create_time", "update_time"]

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = NebulaGraphDb(
            Config(db=db_config, version={"enabled": enable_version})  # type: ignore
        )

    def export_entity_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出属性三元组"""
        entities = self.export_entities(space, skip, limit, version, export_type)
        data = []
        filters = {
            "name": True,
            "_create_time": True,
            "_update_time": True,
        }
        for entity in entities:
            triples = [
                [entity.props["name"], key, entity.props[key]]
                for key in entity.props
                if not filters.get(key)
            ]
            data += triples
        total = len(data)
        return {"total": total, "data": data}

    def export_relation_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出关系三元组"""
        relations = self.export_relations(space, skip, limit, version, export_type)
        data = []
        for relation in relations:
            data.append(
                [
                    relation.src_node.name,
                    relation.props["name"],
                    relation.dst_node.name,
                ]
            )
        total = len(data)
        return {"total": total, "data": data}

    def export_entities(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> list:
        """导出实体"""
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)  # type: ignore
        if export_type == "ontology":
            nodes = graph.nodes.match(types=["concept"]).limit(limit).skip(skip).all()  # type: ignore
        elif export_type == "entity":
            nodes = (  # type: ignore
                graph.nodes.match()
                .where(where_query="NOT n:concept")
                .limit(limit)
                .skip(skip)
                .all()
            )
        else:
            nodes = graph.nodes.match().limit(limit).skip(skip).all()
        return [self._restore_node_props(node) for node in nodes]

    def export_relations(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> list:
        """导出关系"""
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)  # type: ignore
        if export_type == "ontology":
            edges = (  # type: ignore
                graph.edges.match(
                    src_node_types={"concept"}, dst_node_types={"concept"}
                )
                .limit(limit)
                .skip(skip)
                .all()
            )
        elif export_type == "entity":
            edges = (  # type: ignore
                graph.edges.match()
                .where(where_query="NOT n:concept AND NOT m:concept")
                .limit(limit)
                .skip(skip)
                .all()
            )
        else:
            edges = graph.edges.match().limit(limit).skip(skip).all()
        return [self._restore_edge_props(edge) for edge in edges]

    def create_index(self, space: str, name: str = "_eid") -> None:
        """创建索引"""
        pass

    def get_node_by_names(self, space: str, names: list) -> list:
        if not names:
            return []
        graph = self.graph_db.get_graph(space)
        nodes = graph.nodes.match(props=[("name", "in", names)]).all()  # type: ignore
        return [self._restore_node_props(node) for node in nodes]

    def _pre_process_node(self, node: Node) -> None:
        """预处理节点"""
        now_time = TimeUtils.now_str()
        node.props["_create_time"] = now_time
        node.props["_update_time"] = now_time
        if not node.props.get("_type"):
            node.props["_type"] = "其他"
        if node.props.get("_eid"):
            node.id = node.props["_eid"]
        props = {}
        for key, value in node.props.items():
            props[key.strip("_")] = value
        node.props = props

    def upsert_nodes(
        self, space: str, nodes: List[Node], primary_key: str = "name"
    ) -> None:
        """插入多个点"""
        graph = self.graph_db.get_graph(space)
        default_node_type = graph.get_node_type(space)
        prop_names = [prop.name for prop in default_node_type.props]
        node_types = graph.node_types
        has_new_props = False
        for node in nodes:
            self._pre_process_node(node)
            for name in node.types:
                if name not in node_types:
                    graph.create_node_type(NodeType(name=name))
            for key in node.props.keys():
                if key not in prop_names:
                    has_new_props = True
                    prop_names.append(key)
                    default_node_type.props.append(
                        NodeProp(name=key, type="string", nullable=True)
                    )
        if has_new_props:
            graph.alter_node_type(default_node_type)
        graph.upsert_nodes(nodes, primary_key)

    def _pre_process_edge(self, edge: Edge) -> None:
        """预处理边"""
        now_time = TimeUtils.now_str()
        props = {"name": edge.type, "create_time": now_time, "update_time": now_time}
        for key, value in edge.props.items():
            props[key.strip("_")] = value
        edge.props = props

    def insert_edges(self, space: str, edges: List[Edge]) -> None:
        """插入多个边"""
        graph = self.graph_db.get_graph(space)
        edge_types = graph.edge_types
        new_edge_types = []
        for edge in edges:
            self._pre_process_edge(edge)
            if edge.type not in edge_types:
                edge_type = self._format_edge_type(edge.type)
                new_edge_types.append(edge_type)
        if new_edge_types:
            graph.create_edge_types(new_edge_types)
        graph.insert_edges(edges)

    def _format_edge_type(self, name: str) -> EdgeType:
        """创建边类型"""
        return EdgeType(
            name=name,
            props=[
                {"name": "name", "type": "string"},
                {"name": "rid", "type": "string"},
                {"name": "create_time", "type": "string"},
                {"name": "update_time", "type": "string"},
                {"name": "c", "type": "bool"},
                {"name": "id", "type": "string"},
            ],
        )

    @overload
    def _restore_props(self, node_or_edge: Node, private_props: List[str]) -> Node: ...

    @overload
    def _restore_props(self, node_or_edge: Edge, private_props: List[str]) -> Edge: ...

    def _restore_props(
        self, node_or_edge: Union[Node, Edge], private_props: List[str]
    ) -> Union[Node, Edge]:
        props = {}
        for key, value in node_or_edge.props.items():
            if key in private_props:
                props[f"_{key}"] = value
            else:
                props[key] = value
        node_or_edge.props = props
        return node_or_edge

    def _restore_node_props(self, node: Node) -> Node:
        return self._restore_props(node, self.node_private_props)

    def _restore_edge_props(self, edge: Edge) -> Edge:
        edge.src_node = self._restore_node_props(edge.src_node)  # type: ignore
        edge.dst_node = self._restore_node_props(edge.dst_node)  # type: ignore
        return self._restore_props(edge, self.edge_private_props)
