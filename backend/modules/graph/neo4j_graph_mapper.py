from typing import List

from yfflow import YfLogger
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import Neo4jGraphDb
from yunfu.db.graph.models import Edge, Node

from .base_graph_mapper import BaseGraphMapper

logger = YfLogger(__name__)


class Neo4jGraphMapper(BaseGraphMapper):
    graph_db: Neo4jGraphDb

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = Neo4jGraphDb(
            Config(db=db_config, version={"enabled": enable_version})  # type: ignore
        )

    def export_entity_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出属性三元组"""
        entities = self.export_entities(space, skip, limit, version, export_type)
        data = []
        filters = {
            "name": True,
            "_create_time": True,
            "_update_time": True,
        }
        for entity in entities:
            triples = [
                [entity.props["name"], key, entity.props[key]]
                for key in entity.props
                if not filters.get(key)
            ]
            data += triples
        total = len(data)
        return {"total": total, "data": data}

    def export_relation_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出关系三元组"""
        relations = self.export_relations(space, skip, limit, version, export_type)
        data = []
        for relation in relations:
            data.append(
                [
                    relation.src_node.name,     # type: ignore
                    relation.props["name"],
                    relation.dst_node.name,     # type: ignore
                ]
            )
        total = len(data)
        return {"total": total, "data": data}

    def export_entities(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> List[Node]:
        """导出实体"""
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)  # type: ignore
        if export_type == "ontology":
            return graph.nodes.match(types=["concept"]).limit(limit).skip(skip).all()  # type: ignore
        if export_type == "entity":
            return (  # type: ignore
                graph.nodes.match()
                .where(where_query="NOT n:concept")
                .limit(limit)
                .skip(skip)
                .all()
            )
        return graph.nodes.match().limit(limit).skip(skip).all()  # type: ignore

    def export_relations(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> List[Edge]:
        """导出关系"""
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)  # type: ignore
        if export_type == "ontology":
            return (  # type: ignore
                graph.edges.match(
                    src_node_types={"concept"}, dst_node_types={"concept"}
                )
                .limit(limit)
                .skip(skip)
                .all()
            )
        if export_type == "entity":
            return (  # type: ignore
                graph.edges.match()
                .where(where_query="NOT n:concept AND NOT m:concept")
                .limit(limit)
                .skip(skip)
                .all()
            )
        return graph.edges.match().limit(limit).skip(skip).all()  # type: ignore

    def create_index(self, space: str, name: str = "_eid") -> None:
        """创建索引"""
        graph = self.graph_db.get_graph(space)
        query = (
            f"CREATE CONSTRAINT IF NOT EXISTS ON (n:{space}) ASSERT n.{name} IS UNIQUE"
        )
        graph.client.run(query)

    def get_node_by_names(self, space: str, names: List[str]) -> List[Node]:
        if not names:
            return []
        graph = self.graph_db.get_graph(space)
        return graph.nodes.match(props=[("name", "in", names)]).all()  # type: ignore
