import time
from typing import Dict

from yfflow import YfLogger
from yunfu.common import ConfigUtils, yfid
from yunfu.db.graph.deprecated.utils import TimeUtils
from yunfu.db.graph.models import Edge, Node

from backend.models.kg import Kg
from backend.modules.graph import get_graph_mapper
from backend.utils import Timer

conf = ConfigUtils.load("conf/config.yaml")
logger = YfLogger(__name__)


class Inserter:
    def __init__(self, kg: Kg, project: str = "project"):
        self.space = f"KG{kg.id}"
        self.project = project
        self.id = kg.id
        self.graph_mapper = get_graph_mapper(kg.db, kg.db_config)

    def insert_to_neo4j(
        self, ontology_name2node: dict, entity_name2node: dict, edges: list
    ) -> None:
        with Timer("创建索引"):
            self.graph_mapper.create_index(self.space, "name")
            self.graph_mapper.create_index(self.space)
        with Timer("写入本体结点"):
            self._create_nodes(ontology_name2node)
        with Timer("写入实体结点"):
            self._create_nodes(entity_name2node)
        with Timer("写入关系"):
            self._create_relations(edges)

    def _create_nodes(self, nodes: Dict[str, Node]) -> None:
        # 批量创建节点
        save_nodes = []
        num_ = 0
        for name, node in nodes.items():
            node.props["_eid"] = node.props.get("_eid", yfid(Node.get_id(name)))
            node.props["_show_name"] = node.props.get("_show_name", name)
            save_nodes.append(node)
            num_ += 1
            if num_ > 10000:
                self.graph_mapper.upsert_nodes(self.space, save_nodes)
                save_nodes = []
                num_ = 0
        self.graph_mapper.upsert_nodes(self.space, save_nodes)

    def _create_relations(self, edges: list) -> None:
        save_edges = []
        name2node = {}
        nodes_name_ = set()
        for edge in edges:
            nodes_name_.add(edge[0])
            nodes_name_.add(edge[2])
        nodes_name = list(nodes_name_)
        nodes = self.graph_mapper.get_node_by_names(self.space, nodes_name)
        for node in nodes:
            name2node[node.props["name"]] = node
        new_nodes = []
        for index, edge in enumerate(edges, start=1):
            subject = edge[0]
            predicate = edge[1]
            object = edge[2]  # noqa
            if not name2node.get(subject):
                props = {
                    "name": subject,
                    "_eid": Node.get_id(subject),
                    "_show_name": subject,
                    "_type": "其他",
                    "_create_time": TimeUtils.now_str(),
                    "_update_time": TimeUtils.now_str(),
                }
                src_node = Node(
                    id=props["_eid"],
                    types=[conf["version_label"], self.space],
                    props=props,
                )
                new_nodes.append(src_node)
                name2node[subject] = src_node
            else:
                src_node = name2node[subject]
            if not name2node.get(object):
                props = {
                    "name": object,
                    "_eid": Node.get_id(object),
                    "_show_name": object,
                    "_type": "其他",
                    "_create_time": TimeUtils.now_str(),
                    "_update_time": TimeUtils.now_str(),
                }
                if predicate == "属于":
                    dest_node = Node(id=props["_eid"], types=["concept"], props=props)
                else:
                    dest_node = Node(id=props["_eid"], props=props)
                new_nodes.append(dest_node)
                name2node[object] = dest_node
            else:
                dest_node = name2node[object]
            props = {
                "c": True,
                "_rid": yfid(f"{subject}_{predicate}_{object}_{time.time()}")[:6],
                "_create_time": TimeUtils.now_str(),
                "_update_time": TimeUtils.now_str(),
            }
            if "|" in predicate:
                predicates = predicate.strip().split("|")
                for item in predicates:
                    if ":" not in item and "@" not in item:
                        props["name"] = item
                    else:
                        property_, value = item.split(":", 1)
                        props[property_] = value
            else:
                props["name"] = predicate

            relation_type = "属于" if predicate == "属于" else "关联"
            save_edges.append(
                Edge(
                    id=props["_rid"],
                    src_id=src_node.id,     # type: ignore
                    type=relation_type,
                    dst_id=dest_node.id,     # type: ignore
                    props=props,
                )
            )
            if index % 10000 == 0:
                logger.info(f"已处理{index+1}条数据 Now is {time.time()}")
                if new_nodes:
                    logger.info(f"插入关系中不存在结点的数量: {len(new_nodes)}")
                    self.graph_mapper.upsert_nodes(self.space, new_nodes)
                    new_nodes = []
                self.graph_mapper.upsert_edges(self.space, save_edges)
                save_edges = []
        if new_nodes:
            logger.info(f"插入关系中不存在结点的数量: {len(new_nodes)}")
            self.graph_mapper.upsert_nodes(self.space, new_nodes)
        if save_edges:
            self.graph_mapper.upsert_edges(self.space, save_edges)
