minio:
  endpoint: yftool-db-minio:9000
  access_key: xuKIIgW6L7Db55ik
  secret_key: gatXqFGPFRJXDaz4U2lhUAdzyeoDF4N6
  secure: False
minio_host: https://local.pic.yunfutech.com
neo4j:
  schema: bolt
  host: !env ${NEO4J_HOST|yftool-db-neo4j}
  port: !env ${NEO4J_PORT|7687}
  username: !env ${NEO4J_USERNAME|neo4j}
  password: !env ${NEO4J_PASSWORD|yunfu2017}
nebula:
  host: !env ${NEBULA_HOST|*************}
  port: !env ${NEBULA_PORT|9669}
  username: !env ${NEBULA_USERNAME|root}
  password: !env ${NEBULA_PASSWORD|root}
es:
  host: !env ${ES_HOST|http://yftool-db-elasticsearch:920}
  base: !env ${ES_BASE|/opt/yunfu/yfproduct/yfkg2/web/knowledge/linking_v38_20210325.json}
  event:
    host: !env ${EVENT_HOST|http://yftool-db-elasticsearch:9200}
    index: !env ${EVENT_INDEX|event_v1.7}

version_label: c
ontology_label: concept
version_number: 0

RABBITMQ_HOST: yfproduct-yfkm-web-rabbitmq
RABBITMQ_PORT: 5672

PROJECT2INDEX_CONF_NAME:
  # default: null
  yfint: baike_entity_index
  jky: baike_entity_index
  gongxin: baike_entity_index
  AA: baike_entity_index
  ht12: ht12_entity_index

kg_count:
  host: ************
  port: 10000
  protocol: GRPC
kg_es_saver:
  host: ************
  port: 10004
  protocol: GRPC
kg_saver:
  host: ************
  port: 10002
  protocol: GRPC
