from abc import ABC, abstractmethod
from io import BytesIO
from pathlib import Path
from zipfile import ZipFile

from pydantic import BaseModel
from yfflow import <PERSON>f<PERSON>ogger

from backend.models import ImportParams, Kg
from backend.modules.graph import get_graph_mapper
from backend.utils import FileUtils, MinioUtils

from .triple_classifier import TripleClassifier, Triples
from .triple_generator import TripleGenerator

logger = YfLogger(__name__)


class Media(BaseModel):
    name: str
    suffix: str
    content: bytes
    parent_path: str = ""

    @classmethod
    def from_path(cls, path: str, content: bytes) -> "Media":
        path_obj = Path(path)
        parent_path = "/".join(path_obj.parent.as_posix().lstrip("/").split("/")[1:])
        suffix = path_obj.suffix.lstrip(".")
        return cls(
            name=path_obj.stem, suffix=suffix, content=content, parent_path=parent_path
        )

    def to_object_name(self) -> str:
        return f"/{self.parent_path}/{self.name}.{self.suffix}"


class ParserResult(BaseModel):
    triples: Triples = Triples()


class BaseFileParser(ABC):
    """图谱文件解析器基类"""

    @classmethod
    @abstractmethod
    def parse(cls, params: ImportParams) -> ParserResult:
        raise NotImplementedError


class KgdFileParser(BaseFileParser):
    """kgd文件解析器"""

    @classmethod
    def parse(cls, params: ImportParams) -> ParserResult:
        logger.info(f"开始解析: {params.bucket_name} {params.object_name}")
        data = MinioUtils.download(params.bucket_name, params.object_name)
        result = ParserResult()
        file2media = {}
        with ZipFile(BytesIO(data), "r") as zip_f:
            namelist = zip_f.namelist()
            for triple_file in filter(lambda _: _.endswith(".triple"), namelist):
                content = zip_f.open(triple_file).read().decode("utf-8")
                if triple_file == "ontology.triple":
                    triples = TripleClassifier.classify(content, "ontology")
                else:
                    triples = TripleClassifier.classify(content, "entity")
                result.triples += triples
            for media_file in filter(lambda _: not _.endswith(".triple"), namelist):
                data = zip_f.open(media_file).read()
                file2media[media_file] = Media.from_path(media_file, data)
        cls.convert_media_property(params, result.triples, file2media)
        return result

    @staticmethod
    def convert_media_property(
        params: ImportParams, triples: Triples, file2media: dict
    ) -> None:
        """三元组中的媒体文件路径转换为链接"""
        for idx, triple in enumerate(triples.property):
            entity, key, value, _ = triple.strip().split("\t")
            value = value.strip('"')
            if value in file2media:
                object_name = file2media[value].to_object_name()
                md5 = FileUtils.get_file_md5(BytesIO(file2media[value].content))
                if md5 != file2media[value].name:
                    object_name = f"/{md5[0:2]}/{md5[2:4]}/{md5[4:6]}/{md5}.{file2media[value].suffix}"
                bucket_name = params.bucket_name
                if key == "<_avatar>":
                    bucket_name = "avatars"
                    object_name = f"/{md5}.{file2media[value].suffix}"
                if not MinioUtils.exists(bucket_name, object_name):
                    MinioUtils.upload(
                        bucket_name, object_name, file2media[value].content
                    )
                new_value = MinioUtils.get_url(params.bucket_name, object_name)
                if key == "<_avatar>":
                    new_value = f"{md5}.{file2media[value].suffix}"
                logger.info(f"三元组更换媒体文件: {value} => {new_value}")
                triples.property[idx] = f'{entity}\t{key}\t"{new_value}"\t.'


class ExtractFileParser(BaseFileParser):

    @classmethod
    def parse(cls, params: ImportParams) -> ParserResult:
        logger.info(f"开始解析: {params.kg_id} {params.extract_task_id}")
        result = ParserResult()
        kg = Kg.objects.get(id=params.kg_id)
        ontology_id = kg.ontology.id
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        triple_generator = TripleGenerator(graph_mapper)
        ontology_triples_content = triple_generator.generate(
            f"KG{ontology_id}", "c", "ontology"
        )
        triples = TripleClassifier.classify(ontology_triples_content, "ontology")
        result.triples += triples
        extract_triples_content = triple_generator.generate_by_extract(
            params.extract_task_id
        )
        triples = TripleClassifier.classify(extract_triples_content, "entity")
        result.triples += triples
        return result


class FileParserFactory:

    file_type2parser = {
        "kgd": KgdFileParser(),
        "extract": ExtractFileParser(),
    }

    @classmethod
    def get_parser(cls, file_type: str = "kgd") -> BaseFileParser:
        return cls.file_type2parser[file_type]  # type: ignore
