from yfflow import Client
from yunfu.common import ConfigUtils
from yunfu.db.graph.models import Node

from backend.kg_inserter import Inserter
from backend.models import ImportParams
from backend.models.kg import Kg, UpdateTask

from .file_parsers import ParserResult

conf = ConfigUtils.load("conf/config.yaml")


class Neo4jImporter:
    proxy_args = {
        "broker_host": conf["RABBITMQ_HOST"],
        "broker_port": conf["RABBITMQ_PORT"],
    }

    @classmethod
    def handle(cls, params: ImportParams, result: ParserResult):
        kg_id = params.kg_id
        kg = Kg.objects.get(id=kg_id)
        kg.status = 7
        kg.save()

        cls._import_neo4j(kg, result.triples.ontology, "ontology")
        cls._import_neo4j(kg, result.triples.property)
        cls._import_neo4j(kg, result.triples.relation)
        if not UpdateTask.objects.filter(kg__id=kg_id).exists():
            UpdateTask.objects.create(
                name="构建任务",
                update_type=UpdateTask.UpdateTypes.CREATE,
                user=kg.user,
                status=7,
                kg=kg,
            )

        # client = Client(**conf["kg_count"], proxy_args=cls.proxy_args)
        client = Client(**conf["kg_count"])
        client.post(
            "/update_center_node",
            {"kg_id": kg_id, "version_number": conf["version_number"]},
        )
        client.post(
            "/kg_count", {"kg_id": kg_id, "version_number": conf["version_number"]}
        )
        # client = Client(**conf["kg_saver"], proxy_args=cls.proxy_args)
        client = Client(**conf["kg_saver"])
        client.post("/api/save_graph/", {"source": "link_ontology", "kg_id": kg_id})
        # client = Client(**conf["kg_es_saver"], proxy_args=cls.proxy_args)
        client = Client(**conf["kg_es_saver"])
        client.post("/reindex", {"kg_id": kg_id, "version": conf["version_label"]})
        # 生成关联本体
        client.post("/reindex", {"kg_id": f"-{kg_id}"})
        client.post("/update_kbqa", {"kg_id": kg_id, "version": conf["version_label"]})

    @classmethod
    def _import_neo4j(cls, kg: Kg, triples: list, type_: str = "") -> None:
        ontology_name2node, entity_name2node, edges = cls._get_nodes_and_edges(
            triples, type_
        )
        inserter = Inserter(kg=kg)
        inserter.insert_to_neo4j(ontology_name2node, entity_name2node, edges)

    @classmethod
    def _get_nodes_and_edges(cls, triples: list, type_: str = "") -> tuple:
        ontology_name2node: dict = {}
        entity_name2node: dict = {}
        edges = []
        for triple in triples:
            try:
                subject, predicate, object_, _ = triple.strip().split("\t")
                subject, predicate, object_ = (
                    cls._get_out_of_arrow(subject),
                    cls._get_out_of_arrow(predicate),
                    cls._get_out_of_quotation(object_),
                )  # noqa
                if not subject:
                    continue
                if object_.startswith("<"):
                    edges.append([subject, predicate, cls._get_out_of_arrow(object_)])
                elif predicate == "_type" and object_ != "事物":
                    edges.append([subject, "属于", object_])
                    cls._process_property_triple(
                        subject,
                        predicate,
                        object_,
                        ontology_name2node,
                        entity_name2node,
                        type_,
                    )
                else:
                    cls._process_property_triple(
                        subject,
                        predicate,
                        object_,
                        ontology_name2node,
                        entity_name2node,
                        type_,
                    )
            except Exception:
                continue
        return ontology_name2node, entity_name2node, edges

    @staticmethod
    def _get_out_of_arrow(string: str) -> str:
        return string[1:-1]

    @staticmethod
    def _get_out_of_quotation(string: str) -> str:
        return string.replace('"', "").replace("'", "")

    @classmethod
    def _process_property_triple(
        cls,
        subject: str,
        predicate: str,
        object_: str,
        ontology_name2node: dict,
        entity_name2node: dict,
        type_: str = "",
    ) -> None:
        # 1、本体结点处理
        ontology_label = "concept"
        if predicate == "_type" and type_ == "ontology":
            if not ontology_name2node.get(subject):
                ontology_name2node[subject] = Node(
                    types=[ontology_label],
                    props={
                        "name": subject,
                    },
                )
            return
        # 2、实体结点处理
        if (
            not entity_name2node.get(subject)
            and type_ != "ontology"
            and subject != "事物"
        ):
            entity_name2node[subject] = Node(
                props={"name": subject, predicate: object_},
            )
        else:
            entity_name2node[subject].props[predicate] = object_
