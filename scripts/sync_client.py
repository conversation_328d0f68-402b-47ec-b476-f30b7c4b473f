from yfflow import Client

neo4j = {
    "task_id": 2329,
    "file_type": "extract",
    "org_id": "yky",
    "extract_task_id": 2329,
    "user_id": 305,
    "kg_id": 4314,
    "is_import": True,
}
nebula = {
    "task_id": 2330,
    "file_type": "extract",
    "org_id": "yky",
    "extract_task_id": 2330,
    "user_id": 305,
    "kg_id": 4316,
    "is_import": True,
}

client = Client("127.0.0.1", 8016, "GRPC")

# print(client.post("/", neo4j))
print(client.post("/", nebula))
