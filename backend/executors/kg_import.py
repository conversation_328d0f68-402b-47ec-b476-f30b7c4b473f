from typing import Dict

from django.db import close_old_connections
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.models import ImportParams
from backend.modules.file_parsers import FileParserFactory
from backend.modules.importers import Neo4jImporter


class KgImportExecutor(YfExecutor):

    @requests()  # on: 最终服务所建立的路由
    def run(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            close_old_connections()
            params = ImportParams(**doc.tags)
            self.logger.info(f"导入图谱：{params}")
            result = FileParserFactory.get_parser(params.file_type).parse(params)
            Neo4jImporter.handle(params, result)
