from abc import ABC, abstractmethod
from typing import List, Union

from backend.utils import TimeUtils
from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb, Neo4jGraphDb
from yunfu.db.graph.models import Edge, Node


class BaseGraphMapper(ABC):
    graph_db: Union[Neo4jGraphDb, NebulaGraphDb]

    @abstractmethod
    def export_entity_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出属性三元组"""
        raise NotImplementedError

    @abstractmethod
    def export_relation_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出关系三元组"""
        raise NotImplementedError

    @abstractmethod
    def export_entities(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> list:
        """导出实体"""
        raise NotImplementedError

    @abstractmethod
    def export_relations(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> list:
        """导出关系"""
        raise NotImplementedError

    @abstractmethod
    def create_index(self, index: str, name: str = "_eid") -> None:
        """创建索引"""
        raise NotImplementedError

    @abstractmethod
    def get_node_by_names(self, space: str, names: list) -> list:
        raise NotImplementedError

    def _pre_process_node(self, node: Node) -> None:
        """预处理节点"""
        now_time = TimeUtils.now_str()
        node.props["_create_time"] = now_time
        node.props["_update_time"] = now_time
        if not node.props.get("_type"):
            node.props["_type"] = "其他"
        if node.props.get("_eid"):
            node.id = node.props["_eid"]

    def upsert_nodes(
        self, space: str, nodes: List[Node], primary_key: str = "name"
    ) -> None:
        """插入多个点"""
        graph = self.graph_db.get_graph(space)
        for node in nodes:
            self._pre_process_node(node)
        graph.upsert_nodes(nodes, primary_key)

    def _pre_process_edge(self, edge: Edge) -> None:
        """预处理边"""
        now_time = TimeUtils.now_str()
        edge.props["_create_time"] = now_time
        edge.props["_update_time"] = now_time

    def insert_edges(self, space: str, edges: List[Edge]) -> None:
        """插入多个边"""
        graph = self.graph_db.get_graph(space)
        for edge in edges:
            self._pre_process_edge(edge)
        graph.insert_edges(edges)

    def upsert_edges(self, space: str, edges: List[Edge]) -> None:
        """更新或插入边"""
        graph = self.graph_db.get_graph(space)
        for edge in edges:
            self._pre_process_edge(edge)
        graph.upsert_edges(edges)
