import hashlib
import time
from io import BytesIO

from minio import Minio
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>
from yunfu.common import ConfigUtils, SingletonMeta

conf = ConfigUtils.load("conf/config.yaml")
logger = YfLogger(__name__)


class MinioUtils(metaclass=SingletonMeta):
    minio = Minio(**conf["minio"])

    @classmethod
    def download(cls, bucket_name: str, object_name: str):  # type: ignore
        data = None
        try:
            data = cls.minio.get_object(bucket_name, object_name)
            return data.read()
        except Exception as e:
            err_msg = f"文件下载异常: {object_name} -> {e}"
            logger.error(err_msg)
            raise ValueError(err_msg)
        finally:
            if data:
                data.close()
                data.release_conn()

    @classmethod
    def upload(
        cls, bucket_name: str, object_name: str, data: bytes, size: int = 0
    ) -> None:
        logger.info(f"文件上传: {bucket_name} {object_name}")
        bi = BytesIO(data)
        if not size:
            bi.seek(0, 2)
            size = bi.tell()
            bi.seek(0, 0)
        cls.minio.put_object(bucket_name, object_name, bi, length=size)

    @classmethod
    def exists(cls, bucket_name: str, object_name: str) -> bool:
        try:
            cls.minio.get_object(bucket_name, object_name)
        except Exception as e:
            logger.error(f"文件不存在: {object_name} -> {e}")
            return False
        return True

    @classmethod
    def get_url(cls, bucket_name: str, object_name: str) -> str:
        return f'{conf["minio_host"]}' + f"/{bucket_name}/{object_name}".replace(
            "//", "/"
        )


class FileUtils:

    @classmethod
    def get_file_md5(cls, file: BytesIO):
        """计算文件的md5"""
        md5 = hashlib.md5()
        for chunk in iter(lambda: file.read(4096), b""):
            md5.update(chunk)
        return md5.hexdigest()


class Timer:

    def __init__(self, desc: str = "") -> None:
        self.desc = desc
        self.duration = 0

    def __enter__(self):
        self.start = time.time()

    def __exit__(self, exc_type, exc_val, exc_tb):  # noqa
        end = time.time()
        logger.info(f"[{self.desc}] 耗时: {end - self.start:.2f}秒")


class TimeUtils:

    @classmethod
    def now_str(cls) -> str:
        """
        格式化成2016-03-20 11:45:39形式
        """
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
