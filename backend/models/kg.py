import os

import django
from django.conf import settings
from django.db import models
from yunfu.common import ConfigUtils

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()
conf = ConfigUtils.load("conf/config.yaml")

from model_utils.models import SoftDeletableModel  # noqa: E402


class YfModel(models.Model):
    """基础model类"""

    created_at = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name="修改时间", auto_now=True)

    class Meta:
        abstract = True
        ordering = ["id"]


class Team(YfModel):
    """用户组
    key字段在conf中，用于标识机构的唯一性
    原因是Team表原本设计是用于表示部门，本身是不需要唯一key标识的，后续一级team用于表示组织机构，需要唯一标识，如果直接增加Team表的唯一字段key，对于原来的team则要批量生成唯一key，成本较高
    """

    name = models.CharField(max_length=255, verbose_name="组名")
    conf = models.JSONField(verbose_name="组配置", default=dict)
    parent = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, default=None
    )
    description = models.CharField(
        max_length=255, verbose_name="描述", blank=True, null=True, default=""
    )
    is_activate = models.BooleanField(verbose_name="启用状态", default=True)

    class Meta:
        verbose_name = "team"
        db_table = "yunfu_due_common_team"


class GraphDbs(models.TextChoices):
    NEO4J = "neo4j"
    NEBULA = "nebula"


class Kg(SoftDeletableModel, YfModel):
    class KgRanges(models.IntegerChoices):
        PRIVATE = 1  # 私有
        GROUP_OPENNESS = 2  # 组内公开
        TOTAL_OPENNESS = 3  # 完全公开

    class TypeChoices(models.TextChoices):
        ONTOLOGY = "ontology"  # 模版本体
        KG = "kg"  # 图谱
        UPDATE = "update"  # 更新用图谱

    class CropChoices(models.IntegerChoices):
        NOT_CROP = 0
        CROPING = 1
        CROPED = 2

    name = models.CharField("名称", max_length=255)
    description = models.TextField(verbose_name="简介", null=True, blank=True)
    ontology = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="图谱本体",
        related_name="kg_ontology",
    )
    ontology_num = models.IntegerField(verbose_name="本体计数", default=0)
    property_num = models.IntegerField(verbose_name="属性计数", default=0)
    relation_num = models.IntegerField(verbose_name="关系计数", default=0)
    status = models.SmallIntegerField(verbose_name="状态", default=0)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="操作用户"
    )
    type = models.CharField(
        "图谱类型", choices=TypeChoices.choices, default=TypeChoices.KG, max_length=50
    )  # noqa
    team = models.ForeignKey(
        Team, verbose_name="用户组", on_delete=models.CASCADE, blank=True, null=True
    )
    range = models.IntegerField(
        verbose_name="可见范围", choices=KgRanges.choices, blank=True, default=1
    )  # noqa
    parent = models.ForeignKey(
        "self",
        verbose_name="裁剪来源",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        db_column="pid",
        related_name="children",
    )
    center_node_eid = models.CharField(
        verbose_name="中心节点eid", max_length=255, null=True, blank=True, default=None
    )
    hot_entities = models.JSONField(
        verbose_name="热门实体", null=True, blank=True, default=[]
    )
    crop_status = models.SmallIntegerField(
        verbose_name="裁剪状态", choices=CropChoices.choices, default=0
    )
    db = models.CharField(
        verbose_name="图数据库", max_length=20, default=GraphDbs.NEO4J
    )

    class Meta:
        verbose_name = "kg"
        db_table = "yunfu_due_kg_kg"
        ordering = ["-updated_at"]
        permissions = (
            ("/kg/kgs/manage", "版本管理"),
            ("/kg/kgs/share", "图谱共享"),
            ("/kg/kgs/enable", "图谱禁用"),
            ("/kg/kgs/delete", "图谱删除"),
            ("/kg/kgs/read", "查看权限"),
            ("/kg/kgs/edit", "编辑权限"),
            ("/kg/kgs/log", "图谱日志"),
        )

    @property
    def db_config(self) -> dict:
        return conf["neo4j"] if self.db == GraphDbs.NEO4J else conf["nebula"]   # type: ignore

    @classmethod
    def permission(cls) -> tuple:
        return cls._meta.permissions  # type: ignore

    def check_version_number(self, number: int) -> None:
        if number != 0:
            if not self.versions.filter(number=number).exists():
                raise ValueError("请使用正确的版本号")


class BaseCount(YfModel):
    """计数表"""

    kg = models.ForeignKey(Kg, on_delete=models.CASCADE, verbose_name="对应的图谱")
    name = models.CharField("名字", max_length=500)
    count = models.IntegerField(verbose_name="计数", default=0)

    class Meta:
        verbose_name = "计数表"
        db_table = "yunfu_due_kg_basecount"
        ordering = ["-count"]


class KgVersion(YfModel):
    """图谱版本表"""

    class SyncStatusChoices(models.IntegerChoices):
        Synchronized = 1  # 已同步
        Synchronizing = 2  # 同步中

    kg = models.ForeignKey(
        Kg, related_name="versions", on_delete=models.CASCADE, verbose_name="对应的图谱"
    )
    name = models.CharField(verbose_name="版本名称", max_length=255)
    number = models.IntegerField(verbose_name="版本号", default=0)
    description = models.TextField(verbose_name="简介", null=True, blank=True)
    entities = models.ManyToManyField(
        BaseCount, related_name="kg_count_entities", blank=True
    )
    relations = models.ManyToManyField(
        BaseCount, related_name="kg_count_relations", blank=True
    )
    properties = models.ManyToManyField(
        BaseCount, related_name="kg_count_properties", blank=True
    )
    entity_count = models.IntegerField(verbose_name="实体数量", default=0)
    property_count = models.IntegerField(verbose_name="属性数量", default=0)
    relation_count = models.IntegerField(verbose_name="关系数量", default=0)
    center_node_eid = models.CharField(
        verbose_name="中心节点eid", max_length=255, null=True, blank=True, default=None
    )
    hot_entities = models.JSONField(
        verbose_name="热门实体", null=True, blank=True, default=[]
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name="创建用户"
    )
    ontology_file = models.CharField(
        verbose_name="本体三元组文件id", max_length=100, default="", blank=True
    )
    entity_file = models.CharField(
        verbose_name="实体三元组文件id", max_length=100, default="", blank=True
    )
    full_file = models.CharField(
        verbose_name="多模态图谱压缩包",
        max_length=100,
        default="",
        blank=True,
        null=True,
    )
    visible = models.BooleanField(verbose_name="只读用户是否可见", default=False)
    kg_file = models.CharField(
        verbose_name="全量三元组文件id", max_length=100, default="", blank=True
    )
    parent = models.CharField(
        verbose_name="数据来源", max_length=20, default="", blank=True
    )
    sync_status = models.IntegerField(
        verbose_name="本体操作同步状态",
        choices=SyncStatusChoices.choices,
        default=SyncStatusChoices.Synchronized,
    )
    kbqa_question = models.JSONField(
        verbose_name="常见问题", null=True, blank=True, default=[]
    )
    is_removed = models.BooleanField(verbose_name="是否删除", default=False)

    class Meta:
        verbose_name = "kg_version"
        db_table = "yunfu_due_kg_kgversion"
        ordering = ["-created_at"]


class TaskStatus(YfModel):

    class Status(models.IntegerChoices):
        # 与celery一致
        NO_STARTED = 0  # 未开始
        STARTED = 1  # 已开始
        SUCCESS = 2  # 执行完成
        FAILURE = -1  # 执行失败

    _task_status = models.IntegerField(
        verbose_name="任务状态", choices=Status.choices, default=Status.NO_STARTED
    )

    class Meta:
        abstract = True


class UpdateTask(TaskStatus):
    """图谱更新任务"""

    class UpdateTypes(models.IntegerChoices):
        CREATE = 1  # 图谱构建
        INCREMENTAL_UPDATE = 2  # 增量更新
        FULL_UPDATE = 3  # 全量更新
        AUTO_UPDATE = 4  # 自动更新

    class TriggerTypes(models.IntegerChoices):
        MANUAL_UPDATE = 1  # 手动触发
        AUTO_UPDATE = 2  # 定时触发

    ontology = models.ForeignKey(
        Kg,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="本体",
        default=None,
        related_name="tasks",
    )
    name = models.CharField(
        verbose_name="任务名", null=True, blank=True, max_length=100
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name="创建人员",
        blank=True,
        null=True,
    )
    status = models.SmallIntegerField(verbose_name="任务状态", default=0)
    created_at = models.DateTimeField(
        verbose_name="启动时间", auto_now=False, auto_now_add=True
    )
    time_consuming = models.CharField(
        verbose_name="耗时", null=True, blank=True, max_length=100
    )
    update_type = models.IntegerField(
        verbose_name="更新方式", choices=UpdateTypes.choices, null=True
    )
    trigger_type = models.IntegerField(
        verbose_name="触发方式", choices=TriggerTypes.choices, null=True
    )
    description = models.TextField(verbose_name="任务描述", null=True, blank=True)
    upload_file = models.CharField(
        verbose_name="上传的文件", null=True, blank=True, max_length=100
    )
    kg = models.ForeignKey(
        Kg,
        on_delete=models.CASCADE,
        verbose_name="对应主图图谱",
        null=True,
        blank=True,
        default=None,
        related_name="all_tasks",
    )
    update_kg = models.ForeignKey(
        Kg,
        on_delete=models.CASCADE,
        verbose_name="对应所属图谱",
        null=True,
        blank=True,
        default=None,
        related_name="update_tasks",
    )

    class Meta:
        db_table = "yunfu_due_kg_updatetask"


class ExtractTask(TaskStatus):
    """抽取任务表"""

    class ExtractTaskMethod(models.TextChoices):
        """实体关系属性枚举"""

        RDF_XML = "rdf-xml"
        RDF_TTL = "rdf-ttl"
        TRIPLE = "triple"  # 抽取
        XLSX = "xlsx"  # 抽取
        DOCX = "docx"  # 抽取
        HTML = "html"  # html
        PDF = "pdf"  # 模型
        SECOND_DECIMATION = "second_decimation"  # 二次抽取
        PATENT_PDF = "patent_pdf"  # 专利pdf
        TEXT_BOOK = "text_book"  # 教材 pdf
        CNKI_JOURNAL = "CNKI_journal"  # 期刊摘要 html
        HEMATOLOLINE_JOURNAL = "Hematoline_journal"  # 期刊摘要 html
        WFDATA_JOURNAL = "WFDATA_journal"  # 期刊摘要 html
        MEDLIVE_GUIDE = "Medlive_guide"  # 医脉通医学指南 pdf
        NHC_GUIDE = "NHC_guide"  # 卫健委医学指南 PDF
        HEMATIOLINE_GUIDE = "Hematioline_guide"  # 医学指南 HTML

    name = models.CharField(
        verbose_name="任务名", max_length=100, default="", blank=True
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, verbose_name="创建人员", on_delete=models.CASCADE
    )
    status = models.SmallIntegerField(
        verbose_name="状态", default=0, blank=True
    )  # 枚举
    remark = models.CharField(
        verbose_name="备注", max_length=100, default="", blank=True, null=True
    )
    method = models.CharField(
        verbose_name="抽取方式",
        max_length=100,
        null=True,
        choices=ExtractTaskMethod.choices,
    )
    upload_file = models.CharField(
        verbose_name="文件", max_length=100, default="", blank=True
    )

    class Meta:
        verbose_name = "抽取任务"
        db_table = "yunfu_due_data_extracttask"


class Article(YfModel):
    """文章"""

    class ParseStatus(models.IntegerChoices):
        FAILED = -1  # 解析失败
        WAITING = 0  # 待解析
        PARSING = 1  # 解析中
        SUCCESS = 2  # 解析成果

    user_file_id = models.CharField(
        verbose_name="用户上传文件id", max_length=255, default="", blank=True
    )
    name = models.CharField(
        verbose_name="用户上传文件name", max_length=255, default="", blank=True
    )
    content = models.TextField(verbose_name="内容")
    update_task = models.ForeignKey(
        UpdateTask,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="更新任务",
    )
    extract_task = models.ForeignKey(
        ExtractTask,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="抽取任务",
    )
    status = models.SmallIntegerField(
        verbose_name="解析状态", choices=ParseStatus.choices, default=0
    )
    detail = models.JSONField(
        verbose_name="解析详情", null=True, blank=True, default=[]
    )
    parser_type = models.CharField(
        verbose_name="抽取器", max_length=255, default="", blank=True
    )
    ner_domain = models.CharField(
        verbose_name="实体识别模型", max_length=255, default="", blank=True
    )
    relation_domain = models.CharField(
        verbose_name="关系模型", max_length=255, default="", blank=True
    )
    vocabulary_name = models.CharField(
        verbose_name="词义词表", max_length=255, default="", blank=True
    )

    class Meta:
        db_table = "yunfu_due_data_article"
        verbose_name = "抽取非结构化文本"


class TripleData(YfModel):
    """三元组数据表"""

    class TypeChoices(models.IntegerChoices):
        ENTITY = 0  # 实体
        PROPERTY = 1  # 属性
        RELATION = 2  # 关系

    class SourceType(models.IntegerChoices):
        """来源类型
        :param EXTRACT: 抽取
        :param COMPLETE: 补全
        """

        EXTRACT = 0
        COMPLETE = 1

    class StatusType(models.IntegerChoices):
        """状态类型
        :param USER_ADD: 用户新增
        :param USER_FUSE: 用户融合
        :param USER_DELETE: 用户删除
        :param AUTO_ADD: 自动新增
        :param AUTO_FUSE: 自动融合
        :param AUTO_DELETE: 自动删除
        """

        USER_ADD = 0
        USER_FUSE = 1
        USER_DELETE = 2
        AUTO_ADD = 3
        AUTO_FUSE = 4
        AUTO_DELETE = 5

    class ValidStatusType(models.IntegerChoices):
        """约束状态类型
        :param VALID: 有效
        :param INVALID: 无效
        :param USER_DELETE: 用户删除
        """

        VALID = 1
        INVALID = 2
        DELETE = 3

    class AddStatusType(models.IntegerChoices):
        """方向修改本体状态类型
        :param ADD: 可添加
        :param ADDED: 已添加
        """

        ADDABLE = 1
        ADDED = 2
        UNADDABLE = 3

    subject = models.CharField(
        verbose_name="主体", max_length=100, default="", blank=True
    )
    subject_type = models.CharField(
        verbose_name="主体类型", max_length=100, default="", blank=True
    )
    subject_eid = models.CharField(
        verbose_name="主体eid", max_length=100, default="", blank=True
    )
    object = models.CharField(
        verbose_name="客体", max_length=512, default="", blank=True
    )
    object_type = models.CharField(
        verbose_name="客体类型", max_length=100, default="", blank=True
    )
    object_eid = models.CharField(
        verbose_name="客体eid", max_length=100, blank=True, null=True
    )
    predicate = models.CharField(
        verbose_name="谓词", max_length=100, default="", blank=True
    )
    type = models.IntegerField(
        verbose_name="三元组类型", choices=TypeChoices.choices, default=None, blank=True
    )
    file_line = models.IntegerField(verbose_name="行数", default=0, blank=True)
    count = models.IntegerField(verbose_name="次数", default=0, blank=True)
    yfid = models.CharField(verbose_name="唯一标识", max_length=30, blank=True)
    task = models.ForeignKey(
        UpdateTask,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="更新任务",
        default=0,
    )
    extract_task = models.ForeignKey(
        ExtractTask,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="抽取任务",
        default=0,
    )
    file_id = models.CharField(
        verbose_name="文件id", max_length=100, default="", blank=True
    )
    file_name = models.CharField(
        verbose_name="文件名", max_length=100, default="", blank=True
    )
    second_decimation = models.BooleanField(
        verbose_name="二次抽取", null=False, default=False
    )
    extraction_rules = models.CharField(
        verbose_name="抽取函数", max_length=100, default="", blank=True
    )
    status = models.IntegerField(
        verbose_name="状态", choices=StatusType.choices, blank=True, null=True
    )
    source = models.IntegerField(
        verbose_name="来源", choices=SourceType.choices, blank=True, null=True
    )
    oper_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="操作用户",
    )
    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="非结构化文章",
    )
    position = models.JSONField(
        verbose_name="三元组位置", default=dict, blank=True, null=True
    )
    sentence = models.TextField(verbose_name="所在原句", default="", blank=True)
    valid_status = models.IntegerField(
        verbose_name="约束状态",
        choices=ValidStatusType.choices,
        default=ValidStatusType.VALID,
        blank=True,
    )
    message = models.TextField(verbose_name="三元组对比信息", default="", blank=True)
    add_status = models.IntegerField(
        verbose_name="反向修改本体状态",
        choices=AddStatusType.choices,
        default=AddStatusType.ADDABLE,
        blank=True,
    )

    class Meta:
        verbose_name = "三元组数据"
        db_table = "yunfu_due_data_tripledata"

    def __str__(self):
        return f'("subject": {self.subject}, "predicate": {self.predicate}, "object": {self.object})'
