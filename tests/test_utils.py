from backend.utils import MinioUtils


class TestMinioUtils:

    def test_client(self):
        assert MinioUtils() is MinioUtils()

    def test_upload(self):
        minio_utils = MinioUtils()
        minio_utils.upload('test', 'test.txt', b'hello world')
        assert MinioUtils.get_url('test', 'test.txt') == 'https://local.pic.yunfutech.com/test/test.txt'
        minio_utils.download('test', 'test.txt') == b'hello world'
        minio_utils.minio.remove_object('test', 'test.txt')
